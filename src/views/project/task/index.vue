<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <el-row justify="end">
          <el-col :span="24" style="text-align: right;">
            <el-button type="primary" icon="el-icon-plus" @click="handleAddTask">
              新建任务
            </el-button>
            <el-button type="primary" icon="el-icon-menu" @click="handleBatchAdd" style="margin-left: 8px;">
              批量新增
            </el-button>
          </el-col>
        </el-row>
        <el-row :gutter="15" align="middle" style="margin-top: 18px;">
          <el-col :span="5">
            <el-radio-group v-model="filterForm.taskType" @change="handleTabChange">
              <el-radio-button label="assigned">指派给我</el-radio-button>
              <el-radio-button label="created">由我指派</el-radio-button>
              <el-radio-button label="check">待我审核</el-radio-button>
            </el-radio-group>
          </el-col>
          <el-col :span="3">
            <div style="position: relative;">
              <el-input
                v-model="selectedProjectName"
                placeholder="按项目搜索"
                readonly
                clearable
                @clear="clearProject"
                @click.native="showProjectPicker"
                style="width: 100%; cursor: pointer;"
              >
              </el-input>
            </div>
          </el-col>


          <el-col :span="3">
            <el-input v-model="filterForm.taskName" placeholder="搜索任务名称" clearable/>
          </el-col>


          <el-col :span="5">
            <el-date-picker v-model="filterForm.deadline" type="daterange" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" placeholder="预计完成时间" clearable
                            style="width: 100%"
            />
          </el-col>
          <el-col :span="5">
            <el-select v-model="filterForm.status" multiple placeholder="任务状态" clearable style="width: 100%"
              :multiple-limit="6" :validate-event="false"
              @change="handleStatusChange"
            >
              <el-option label="进行中" value="1"/>
              <el-option label="已完成" value="2"/>
              <el-option label="已暂停" value="3"/>
              <el-option label="验收通过" value="4"/>
              <el-option label="审核驳回" value="5"/>
              <el-option label="任务关闭" value="6"/>
            </el-select>
          </el-col>
          <el-col :span="3" class="filter-actions">
            <el-button type="primary" @click="handleFilter">搜索</el-button>
            <el-button icon="el-icon-download" @click="handleExport">导出</el-button>
          </el-col>
        </el-row>

      </div>
      <!-- 表格视图 -->
      <el-table v-if="viewMode === 'table'" v-loading="loading" :data="taskList" border row-key="id"
                :tree-props="{ children: 'children' }" default-expand-all
      >
        <el-table-column prop="task_name" label="任务名称" min-width="200">
          <template slot-scope="scope">
            <span style="cursor:pointer;color:#409EFF" @click="handleView(scope.row)">
              <i
                :class="scope.row.children && scope.row.children.length > 0 ? 'el-icon-folder-opened' : 'el-icon-document'"
                :style="{ color: scope.row.children && scope.row.children.length > 0 ? '#409EFF' : '#67C23A', marginRight: '6px' }"
              ></i>
              {{ scope.row.task_name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="user_name" label="发布人" width="100"/>
        <el-table-column prop="receive_name"  label="执行人" width="100" align="center"/>
        <el-table-column prop="check_user_name" label="审核人" width="100"/>
        <el-table-column prop="task_level_name" label="优先级" width="70" align="center"/>
        <el-table-column prop="start_time" label="计划开始时间" width="140"/>
        <el-table-column prop="end_time" label="计划结束时间" width="140"/>
        <el-table-column prop="complete_time" label="完成时间" width="140"/>
        <el-table-column prop="task_score" label="评分" width="50"  align="center"/>
        <el-table-column prop="status" label="状态" width="80"/>
        <el-table-column prop="is_beyond" label="超期" width="80"/>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <div style="display: flex; flex-wrap: wrap; gap: 6px 0;">
              <template v-if="filterForm.taskType === 'assigned'">
                <div style="display: flex; width: 100%;">
                  <el-button size="mini" type="success" @click="handleFinish(scope.row)" style="flex:1;">完成</el-button>
                  <el-button size="mini" type="warning" @click="handlePause(scope.row)" style="flex:1;">暂停</el-button>
                </div>
                <div style="display: flex; width: 100%; margin-top: 6px;">
                  <el-button size="mini" type="primary" @click="handleSplitTask(scope.row)" style="flex:1;" v-if="!scope.row.pid || scope.row.pid === 0">拆分</el-button>
                </div>
              </template>
              <template v-else-if="filterForm.taskType === 'created'">
                <div style="display: flex; width: 100%;">
                  <el-button size="mini" type="primary" @click="handleEdit(scope.row)" style="flex:1;">编辑</el-button>
                  <el-button size="mini" type="danger" @click="handleDelete(scope.row)" style="flex:1;">删除</el-button>
                </div>
                <div style="display: flex; width: 100%; margin-top: 6px;">
                  <el-button size="mini" type="warning" @click="handlePause(scope.row)" style="flex:1;">暂停</el-button>
                  <el-button size="mini" type="primary" @click="handleSplitTask(scope.row)" style="flex:1;" v-if="!scope.row.pid || scope.row.pid === 0">拆分</el-button>
                </div>
              </template>
              <template v-else-if="filterForm.taskType === 'check'">
                <div style="display: flex; width: 100%;">
                  <el-button size="mini" type="info" @click="handleReview(scope.row)" style="flex:1;">评审</el-button>
                </div>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 空状态 -->
      <el-empty v-if="!loading && taskList.length === 0" description="暂无任务数据">
      </el-empty>
    </el-card>

    <!-- 项目选择组件 -->
    <SelectProduct
      ref="selectProduct"
      title="选择项目"
      :multiple="false"
      @select="handleProjectSelect"
    />

    <!-- 详情抽屉 -->
    <el-drawer
      :title="dialogTitle"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="500px"
      custom-class="task-detail-drawer"
    >
      <el-form ref="taskFormRef" :model="taskForm" :rules="taskRules" label-width="100px" style="padding: 20px;">
        <el-form-item label="所属项目" prop="projectId">
          <el-input
            v-model="taskForm.projectName"
            placeholder="请选择项目"
            readonly
            clearable
            @clear="clearTaskProject"
            @click.native="showTaskProjectPicker"
            style="cursor: pointer;"
          />
        </el-form-item>
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="执行人" prop="assignee">
          <el-select v-model="taskForm.assignee" placeholder="请选择执行人" filterable>
            <el-option v-for="user in companyUsers" :key="user.id" :label="user.user_username" :value="user.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核人" prop="checkUserId">
          <el-select v-model="taskForm.checkUserId" placeholder="请选择审核人" filterable>
            <el-option v-for="user in companyUsers" :key="user.id" :label="user.user_username" :value="user.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="taskForm.priority" placeholder="请选择优先级">
            <el-option v-for="level in taskLevelList" :key="level.id" :label="level.label" :value="level.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务类型" prop="taskCategory">
          <el-select v-model="taskForm.taskCategory" placeholder="请选择任务类型">
            <el-option v-for="cat in taskCategoryList" :key="cat.id" :label="cat.task_name" :value="cat.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker v-model="taskForm.startTime" type="date" placeholder="选择开始日期" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="截止日期" prop="deadline">
          <el-date-picker v-model="taskForm.deadline" type="date" placeholder="选择截止日期" style="width: 100%;" />
        </el-form-item>
        <div style="margin-top: 24px; display: flex; gap: 10px; justify-content: flex-end;">
          <el-button size="mini" @click="drawerVisible = false">取消</el-button>
          <el-button size="mini" type="primary" @click="submitTaskForm">提交</el-button>
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
import SelectProduct from '@/components/SelectProduct/index.vue'
import {
  addTask,
  delTask, dismantleTask, editTask,
  evaluation,
  fetchList,
  getCompanyUsers,
  getproList,
  getProUser,
  getTaskCategory,
  getTaskLevel, projectEdit,
  taskEnd
} from '@/api/porject/project'

export default {
  name: 'TaskManagement',
  components: {
    SelectProduct
  },
  data() {
    return {
      filterForm: {
        taskType: 'assigned',
        taskName: '',
        status: ['1'], // 默认“进行中”
        priority: '',
        assignee: '',
        deadline: [],
        project: ''
      },
      stats: {
        pending: 12,
        processing: 8,
        completed: 25,
        total: 45
      },
      taskList: [],
      loading: false,
      viewMode: 'table',
      // pagination 已移除
      drawerVisible: false,
      dialogTitle: '',
      taskForm: {
        id: null,
        projectId: '',
        projectName: '',
        taskName: '',
        assignee: '',
        checkUserId: '',
        priority: '',
        taskCategory: '',
        startTime: '',
        deadline: '',
        pid: 0
      },
      selectedProject: null,
      selectedProjectName: '',
      taskRules: {
        projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
        taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        assignee: [{ required: true, message: '请选择执行人', trigger: 'change' }],
        checkUserId: [{ required: true, message: '请选择审核人', trigger: 'change' }],
        priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
        taskCategory: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
        startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
        deadline: [{ required: true, message: '请选择截止日期', trigger: 'change' }]
      },
      companyUsers: [],
      taskLevelList: [],
      taskCategoryList: []
    }
  },
  created() {
    this.fetchList()
    this.getCompanyUsers()
    this.getTaskLevel()
    this.getTaskCategory()
  },
  methods: {

    showProjectPicker() {
      this.$nextTick(() => {
        if (this.$refs.selectProduct && this.$refs.selectProduct.showProjectPicker) {
          this.$refs.selectProduct.showProjectPicker()
        } else {
          this.$message.error('项目选择组件未正确加载')
        }
      })
    },

    handleProjectSelect(project) {
      // 判断是筛选区域还是新增任务表单的项目选择
      if (this.drawerVisible) {
        // 新增任务表单
        this.handleTaskProjectSelect(project)
      } else {
        // 筛选区域
        this.selectedProject = project
        this.selectedProjectName = project ? project.pro_name : ''
        this.filterForm.project = project ? project.id : ''
        this.handleFilter() // 触发搜索
      }
    },

    clearProject() {
      this.selectedProject = null
      this.selectedProjectName = ''
      this.filterForm.project = ''
      this.handleFilter()
    },
    async fetchList() {
      this.loading = true
      try {
        const params = {
          type: this.filterForm.taskType,
          task_name: this.filterForm.taskName,
          status: Array.isArray(this.filterForm.status) ? this.filterForm.status.join(',') : this.filterForm.status,
          // 其它筛选条件可继续加
          // priority: this.filterForm.priority,
          // assignee: this.filterForm.assignee,
          // deadline: this.filterForm.deadline,
          // project: this.filterForm.project
        }
        const res = await fetchList(params)
        this.taskList = res.data || []
      } catch (e) {
        this.$message.error('获取任务列表失败')
      } finally {
        this.loading = false
      }
    },
    handleFilter() {
      // 搜索逻辑需要根据后端API调整
      this.fetchList()
    },
    handleTabChange(val) {
      if (val === 'assigned') {
        this.filterForm.status = ['1', '5'] // 进行中、审核驳回
      } else if (val === 'created') {
        this.filterForm.status = ['1', '2', '3'] // 进行中、已完成、已暂停
      } else if (val === 'check') {
        this.filterForm.status = ['2'] // 已完成
      } else {
        this.filterForm.status = []
      }
      this.handleFilter()
    },
    handleReset() {
      this.filterForm = {
        taskType: 'assigned',
        taskName: '',
        status: ['1', '5'], // 重置为指派给我的默认
        priority: '',
        assignee: '',
        deadline: [],
        project: ''
      }
      this.handleFilter()
    },
    handleAddTask(row) {
      this.dialogTitle = '新建任务'
      this.taskForm = {
        id: null,
        projectId: '',
        projectName: '',
        taskName: '',
        assignee: '',
        checkUserId: '',
        priority: '',
        taskCategory: '',
        startTime: '',
        deadline: '',
        pid: 0
      }
      this.drawerVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '编辑任务'
      this.taskForm = { ...row }
      this.drawerVisible = true
    },
    handleSplitTask(row) {
      this.dialogTitle = '拆分任务'
      this.taskForm = {
        id: null,
        projectId: row.project_id || '',
        projectName: row.project_name || '',
        taskName: '',
        assignee: '',
        checkUserId: '',
        priority: '',
        taskCategory: '',
        startTime: '',
        deadline: '',
        pid: row.id // 设置为被拆分任务的ID
      }
      this.drawerVisible = true
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该任务, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await delTask({ task_id: row.id })
          if (response.meta.status === 200) {
            this.$notify({
              title: 'Success',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.fetchList() // 刷新列表
          }
        } catch (error) {
          // 检查是否是424状态码
          if (error.response && error.response.status === 424) {
            this.$notify({
              title: 'Warning',
              message: '请先删除子集任务',
              type: 'warning',
              duration: 3000
            })
          } else {
            this.$notify({
              title: 'Error',
              message: '删除失败',
              type: 'error',
              duration: 2000
            })
          }
        }
      }).catch(() => {
        this.$notify({
          title: 'Info',
          message: '已取消删除',
          type: 'info',
          duration: 2000
        })
      })
    },
    // 移除分页相关方法
    // handleSizeChange(size) { ... }
    // handleCurrentChange(page) { ... }
    getPriorityType(priority) {
      const types = { '高': 'danger', '中': 'warning', '低': 'info' }
      return types[priority] || 'info'
    },
    getStatusType(status) {
      const types = { '待处理': 'info', '进行中': 'warning', '已完成': 'success' }
      return types[status] || 'info'
    },
    handleSubmit() {
      this.$refs.taskForm.validate(valid => {
        if (valid) {
          this.$message.success('提交成功')
          this.dialogVisible = false
          this.fetchList()
        } else {
          this.$message.error('请检查输入')
          return false
        }
      })
    },
    handleExport() {
      this.$message.success('导出功能待实现')
      // 实际导出逻辑，例如调用后端API或生成Excel文件
    },
    handleRefresh() {
      this.fetchList()
    },
    handleView(row) {
      this.dialogTitle = '任务详情'
      this.taskForm = { ...row }
      this.drawerVisible = true
    },
    handleBatchAdd() {
      this.$message.info('批量新增功能待实现')
    },
    handleFinish(row) {
      // 完成任务的逻辑
      this.$confirm('确定要完成这个任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 调用evaluation接口，传参task_type为task_end
          const response = await evaluation({
            task_id: row.id,
            task_type: 'task_end'
          })

          if (response.meta.status === 200) {
            this.$notify({
              title: 'Success',
              message: '任务完成成功',
              type: 'success',
              duration: 2000
            })
            // 刷新任务列表
            this.fetchList()
          }
        } catch (error) {
          this.$notify({
            title: 'Error',
            message: '完成任务失败',
            type: 'error',
            duration: 2000
          })
        }
      }).catch(() => {
        this.$notify({
          title: 'Info',
          message: '已取消完成任务',
          type: 'info',
          duration: 2000
        })
      })
    },
    handlePause(row) {
      this.$message.info('暂停功能待实现')
    },
    handleReview(row) {
      this.$message.info('评审功能待实现')
    },
    handleStatusChange(val) {
      if (!val || val.length === 0) {
        // 至少选中一个，回退为上一次状态
        this.$nextTick(() => {
          if (this.filterForm.taskType === 'assigned') {
            this.filterForm.status = ['1', '5']
          } else if (this.filterForm.taskType === 'created') {
            this.filterForm.status = ['1', '2', '3']
          } else if (this.filterForm.taskType === 'check') {
            this.filterForm.status = ['2']
          } else {
            this.filterForm.status = ['1']
          }
        })
        this.$message.warning('任务状态至少选择一个')
      }
    },
    async getCompanyUsers() {
      const res = await getCompanyUsers()
      this.companyUsers = res.data || []
    },
    async getTaskLevel() {
      const res = await getTaskLevel()
      this.taskLevelList = Object.keys(res.data).map(id => ({ id, label: res.data[id] }))
    },
    async getTaskCategory() {
      const res = await getTaskCategory()
      this.taskCategoryList = res.data || []
    },
    submitTaskForm() {
      this.$refs.taskFormRef.validate(async valid => {
        if (!valid) return

        const params = {
          task_name: this.taskForm.taskName,
          receive_id: this.taskForm.assignee,
          check_user_id: this.taskForm.checkUserId,
          task_level: this.taskForm.priority,
          task_category: this.taskForm.taskCategory,
          start_time: this.taskForm.startTime,
          end_time: this.taskForm.deadline,
          pid: this.taskForm.pid,
          project_id: this.taskForm.projectId
        }

        try {
          let response

          // 判断操作类型
          if (this.taskForm.id) {
            // 编辑任务
            params.task_id = this.taskForm.id
            response = await editTask(params)
            this.$notify({
              title: 'Success',
              message: '编辑成功',
              type: 'success',
              duration: 2000
            })
          } else if (this.taskForm.pid && this.taskForm.pid !== 0) {
            // 拆分任务
            response = await dismantleTask(params)
            this.$notify({
              title: 'Success',
              message: '拆分成功',
              type: 'success',
              duration: 2000
            })
          } else {
            // 新增任务
            response = await addTask(params)
            this.$notify({
              title: 'Success',
              message: '添加成功',
              type: 'success',
              duration: 2000
            })
          }

          if (response.meta.status === 200) {
            this.drawerVisible = false
            this.fetchList() // 刷新列表
          }
        } catch (error) {
          this.$notify({
            title: 'Error',
            message: '操作失败',
            type: 'error',
            duration: 2000
          })
        }
      })
    },
    showTaskProjectPicker() {
      this.$nextTick(() => {
        if (this.$refs.selectProduct && this.$refs.selectProduct.showProjectPicker) {
          this.$refs.selectProduct.showProjectPicker()
        } else {
          this.$message.error('项目选择组件未正确加载')
        }
      })
    },
    handleTaskProjectSelect(project) {
      this.taskForm.projectId = project ? project.id : ''
      this.taskForm.projectName = project ? project.pro_name : ''
    },
    clearTaskProject() {
      this.taskForm.projectId = ''
      this.taskForm.projectName = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}


.stats-row {
  margin-bottom: 20px;

  .stats-card {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .stats-content {
      display: flex;
      align-items: center;

      .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 24px;
        color: white;

        &.pending {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.processing {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.completed {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.total {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }

      .stats-info {
        .stats-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
        }

        .stats-label {
          font-size: 14px;
          color: #909399;
          margin-top: 5px;
        }
      }
    }
  }
}

.filter-card {
  margin-bottom: 20px;

  .filter-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }

  .filter-label {
    margin-right: 10px;
    font-weight: 500;
    color: #606266;
  }

  .filter-actions {
    display: flex;
    gap: 10px;
  }
}

.el-card {
  .el-card__header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-actions {
      display: flex;
      gap: 10px;
    }
  }
}

.table-actions {
  margin-bottom: 16px;
  text-align: right;
}


.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.subtask-list {
  margin-top: 12px;

  .subtask-card {
    margin-bottom: 8px;
    margin-left: 18px;
    background: #f8fafd;
    border-left: 3px solid #409EFF;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
  }

  .stats-row {
    .el-col {
      margin-bottom: 15px;
    }
  }

  .card-view {
    .el-col {
      width: 100% !important;
    }
  }
}

::v-deep .el-table .el-table__cell {
  vertical-align: middle;
}

::v-deep .task-name-cell {
  display: flex;
  align-items: center;
}

::v-deep .el-table__indent {
  width: 32px !important;
}
</style>
